import 'dart:io';
import 'package:flutter/material.dart';
import '../models/service_model.dart';
import '../services/service_api_service.dart';

class ServicesProvider with ChangeNotifier {
  List<ServiceModel> _services = [];
  bool _isLoading = false;
  bool _isCreating = false;
  bool _isUpdating = false;
  bool _isDeleting = false;
  String? _error;
  ServiceModel? _selectedService;

  // Getters
  List<ServiceModel> get services => _services;
  bool get isLoading => _isLoading;
  bool get isCreating => _isCreating;
  bool get isUpdating => _isUpdating;
  bool get isDeleting => _isDeleting;
  String? get error => _error;
  bool get hasServices => _services.isNotEmpty;
  ServiceModel? get selectedService => _selectedService;

  // Private setters
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  void _setCreating(bool value) {
    _isCreating = value;
    notifyListeners();
  }

  void _setUpdating(bool value) {
    _isUpdating = value;
    notifyListeners();
  }

  void _setDeleting(bool value) {
    _isDeleting = value;
    notifyListeners();
  }

  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Load all business services
  Future<void> loadBusinessServices({
    int page = 1,
    int limit = 10,
    String? category,
    bool refresh = false,
  }) async {
    if (_isLoading && !refresh) return; // Prevent multiple simultaneous calls

    _setLoading(true);
    _setError(null);

    try {
      final services = await ServiceApiService.getBusinessServices(
        page: page,
        limit: limit,
        category: category,
      );
      
      if (refresh || page == 1) {
        _services = services;
      } else {
        _services.addAll(services);
      }
      
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Get service by ID
  Future<void> getServiceById(String serviceId) async {
    try {
      _setLoading(true);
      _setError(null);

      final service = await ServiceApiService.getServiceById(serviceId);
      _selectedService = service;
      notifyListeners();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Create new service
  Future<bool> createService(
    ServiceRequest serviceRequest, {
    List<File>? imageFiles,
  }) async {
    try {
      _setCreating(true);
      _setError(null);

      final newService = await ServiceApiService.createService(
        serviceRequest,
        imageFiles: imageFiles,
      );

      // Add to the beginning of the list
      _services.insert(0, newService);
      notifyListeners();

      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setCreating(false);
    }
  }

  // Update existing service
  Future<bool> updateService(
    String serviceId,
    ServiceRequest serviceRequest, {
    List<File>? imageFiles,
  }) async {
    try {
      _setUpdating(true);
      _setError(null);

      final updatedService = await ServiceApiService.updateService(
        serviceId,
        serviceRequest,
        imageFiles: imageFiles,
      );

      // Update the service in the list
      final index = _services.indexWhere((service) => service.id == serviceId);
      if (index != -1) {
        _services[index] = updatedService;
        notifyListeners();
      }

      // Update selected service if it's the same
      if (_selectedService?.id == serviceId) {
        _selectedService = updatedService;
      }

      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setUpdating(false);
    }
  }

  // Delete service
  Future<bool> deleteService(String serviceId) async {
    try {
      _setDeleting(true);
      _setError(null);

      final success = await ServiceApiService.deleteService(serviceId);

      if (success) {
        // Remove from the list
        _services.removeWhere((service) => service.id == serviceId);
        
        // Clear selected service if it's the deleted one
        if (_selectedService?.id == serviceId) {
          _selectedService = null;
        }
        
        notifyListeners();
      }

      return success;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setDeleting(false);
    }
  }

  // Clear error
  void clearError() {
    _setError(null);
  }

  // Clear selected service
  void clearSelectedService() {
    _selectedService = null;
    notifyListeners();
  }

  // Refresh services
  Future<void> refreshServices() async {
    await loadBusinessServices(refresh: true);
  }

  // Get service by index (for UI convenience)
  ServiceModel? getServiceByIndex(int index) {
    if (index >= 0 && index < _services.length) {
      return _services[index];
    }
    return null;
  }

  // Search services locally (can be enhanced with API search)
  List<ServiceModel> searchServices(String query) {
    if (query.isEmpty) return _services;
    
    final lowercaseQuery = query.toLowerCase();
    return _services.where((service) {
      return service.title.toLowerCase().contains(lowercaseQuery) ||
             (service.description?.toLowerCase().contains(lowercaseQuery) ?? false) ||
             (service.category?.name.toLowerCase().contains(lowercaseQuery) ?? false);
    }).toList();
  }

  // Filter services by category
  List<ServiceModel> filterByCategory(String categoryId) {
    return _services.where((service) => service.categoryId == categoryId).toList();
  }

  // Get active services only
  List<ServiceModel> get activeServices {
    return _services.where((service) => service.isActive).toList();
  }
}
