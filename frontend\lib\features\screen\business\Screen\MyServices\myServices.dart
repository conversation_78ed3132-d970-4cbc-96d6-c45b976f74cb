import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:petcare/common/widgets/Button/primarybutton.dart';
import 'package:petcare/utlis/constants/colors.dart';
import '../../../../../common/widgets/appbar/appbar.dart';
import '../../../../../utlis/constants/size.dart';
import '../../../../../provider/services_provider.dart';
import '../../../../../models/service_model.dart';
import 'addnewServices.dart';
import 'editServicesdetails.dart';

class MyServices extends StatefulWidget {
  const MyServices({super.key});

  @override
  State<MyServices> createState() => _MyServicesState();
}

class _MyServicesState extends State<MyServices> {
  @override
  void initState() {
    super.initState();
    // Load services when the screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<ServicesProvider>().loadBusinessServices();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: 'My Services'),
      body: Consumer<ServicesProvider>(
        builder: (context, servicesProvider, child) {
          return SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: AppSizes.defaultPaddingHorizontal,
                vertical: AppSizes.defaultPaddingVertical,
              ),
              child: Column(
                children: [
                  PrimaryButton(
                    title: 'Add New Service',
                    onPressed: () {
                      Get.to(() => const AddNewServices());
                    },
                  ),
                  SizedBox(height: 30.h),
                  _buildServicesContent(servicesProvider),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildServicesContent(ServicesProvider servicesProvider) {
    if (servicesProvider.isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (servicesProvider.error != null) {
      return _buildErrorWidget(servicesProvider);
    }

    if (!servicesProvider.hasServices) {
      return _buildEmptyState();
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: servicesProvider.services.length,
      itemBuilder: (context, index) {
        final service = servicesProvider.services[index];
        return Card(
          elevation: 3,
          margin: EdgeInsets.symmetric(vertical: 8.h),
          color: AppColors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppSizes.borderRadiusMd),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 14.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        service.title,
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              fontSize: 14.sp,
                              color: AppColors.primary,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        // Convert ServiceModel to Map for compatibility
                        final serviceMap = {
                          'title': service.title,
                          'description': service.description ?? '',
                          'price': service.price ?? '',
                          'id': service.id,
                        };
                        Get.to(() => EditServicesDetails(service: serviceMap));
                      },
                      child: Icon(
                        Icons.edit_outlined,
                        color: AppColors.primary,
                        size: AppSizes.iconSm,
                      ),
                    ),
                    SizedBox(width: 10.w),
                    InkWell(
                      onTap: () =>
                          _showDeleteDialog(context, service, servicesProvider),
                      child: Icon(
                        Icons.delete,
                        color: const Color(0xFFFB2828),
                        size: AppSizes.iconSm,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.h),
                Text(
                  service.description ?? '',
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        fontSize: 13.sp,
                        color: AppColors.primary,
                      ),
                ),
                SizedBox(height: 12.h),
                Row(
                  children: [
                    Text(
                      "Starting from ${service.price ?? 'N/A'}",
                      style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                            fontSize: 14.sp,
                            color: AppColors.dottedColor,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    SizedBox(width: 8.w),
                    Icon(
                      Icons.arrow_forward,
                      color: AppColors.dottedColor,
                      size: AppSizes.iconMd,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorWidget(ServicesProvider servicesProvider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.sp,
            color: Colors.red,
          ),
          SizedBox(height: 16.h),
          Text(
            'Error loading services',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: 8.h),
          Text(
            servicesProvider.error ?? 'Unknown error',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          PrimaryButton(
            title: 'Retry',
            onPressed: () {
              servicesProvider.clearError();
              servicesProvider.loadBusinessServices();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.business_center_outlined,
            size: 64.sp,
            color: AppColors.dottedColor,
          ),
          SizedBox(height: 16.h),
          Text(
            'No Services Yet',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: 8.h),
          Text(
            'Add your first service to get started',
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, ServiceModel service,
      ServicesProvider servicesProvider) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Delete Service'),
          content: Text('Are you sure you want to delete "${service.title}"?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(dialogContext).pop();
                final success =
                    await servicesProvider.deleteService(service.id);
                if (mounted) {
                  if (success) {
                    ScaffoldMessenger.of(this.context).showSnackBar(
                      const SnackBar(
                          content: Text('Service deleted successfully')),
                    );
                  } else {
                    ScaffoldMessenger.of(this.context).showSnackBar(
                      SnackBar(
                          content: Text(servicesProvider.error ??
                              'Failed to delete service')),
                    );
                  }
                }
              },
              child: const Text('Delete', style: TextStyle(color: Colors.red)),
            ),
          ],
        );
      },
    );
  }
}
