import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/service_model.dart';
import '../utlis/app_config/app_config.dart';
import 'api_service.dart';

class ServiceApiService {
  static final String baseUrl = AppConfig.baseUrl;

  // Get headers with authentication
  static Future<Map<String, String>> _getHeaders({bool requireAuth = true}) async {
    Map<String, String> headers = {
      'Content-Type': 'application/json',
    };

    if (requireAuth) {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      if (token != null) {
        headers['Authorization'] = 'Bearer $token';
      }
    }

    return headers;
  }

  // Get headers for multipart requests
  static Future<Map<String, String>> _getMultipartHeaders() async {
    Map<String, String> headers = {};

    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString('auth_token');
    if (token != null) {
      headers['Authorization'] = 'Bearer $token';
    }

    return headers;
  }

  // Get all business services
  static Future<List<ServiceModel>> getBusinessServices({
    int page = 1,
    int limit = 10,
    String? category,
  }) async {
    try {
      Map<String, String> queryParams = {
        'page': page.toString(),
        'limit': limit.toString(),
      };

      if (category != null && category.isNotEmpty) {
        queryParams['category'] = category;
      }

      final response = await ApiService.get(
        '/services/business/all',
        requireAuth: true,
        queryParams: queryParams,
      );

      if (response['services'] != null) {
        final List<dynamic> servicesJson = response['services'];
        return servicesJson.map((json) => ServiceModel.fromJson(json)).toList();
      }

      return [];
    } catch (e) {
      throw Exception('Failed to fetch business services: ${e.toString()}');
    }
  }

  // Get service by ID
  static Future<ServiceModel?> getServiceById(String serviceId) async {
    try {
      final response = await ApiService.get(
        '/services/$serviceId',
        requireAuth: true,
      );

      if (response['service'] != null) {
        return ServiceModel.fromJson(response['service']);
      }

      return null;
    } catch (e) {
      throw Exception('Failed to fetch service: ${e.toString()}');
    }
  }

  // Create service with images
  static Future<ServiceModel> createService(
    ServiceRequest serviceRequest, {
    List<File>? imageFiles,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl/services/create');
      final request = http.MultipartRequest('POST', uri);

      // Add headers
      final headers = await _getMultipartHeaders();
      request.headers.addAll(headers);

      // Add service data
      final serviceData = serviceRequest.toJson();
      serviceData.forEach((key, value) {
        if (value != null) {
          if (value is Map || value is List) {
            request.fields[key] = json.encode(value);
          } else {
            request.fields[key] = value.toString();
          }
        }
      });

      // Add image files
      if (imageFiles != null && imageFiles.isNotEmpty) {
        for (int i = 0; i < imageFiles.length; i++) {
          final file = imageFiles[i];
          final multipartFile = await http.MultipartFile.fromPath(
            'images',
            file.path,
          );
          request.files.add(multipartFile);
        }
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      final responseBody = json.decode(response.body);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ServiceModel.fromJson(responseBody['service']);
      } else {
        throw Exception(responseBody['message'] ?? 'Failed to create service');
      }
    } catch (e) {
      throw Exception('Failed to create service: ${e.toString()}');
    }
  }

  // Update service with images
  static Future<ServiceModel> updateService(
    String serviceId,
    ServiceRequest serviceRequest, {
    List<File>? imageFiles,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl/services/update/$serviceId');
      final request = http.MultipartRequest('PUT', uri);

      // Add headers
      final headers = await _getMultipartHeaders();
      request.headers.addAll(headers);

      // Add service data
      final serviceData = serviceRequest.toJson();
      serviceData.forEach((key, value) {
        if (value != null) {
          if (value is Map || value is List) {
            request.fields[key] = json.encode(value);
          } else {
            request.fields[key] = value.toString();
          }
        }
      });

      // Add image files
      if (imageFiles != null && imageFiles.isNotEmpty) {
        for (int i = 0; i < imageFiles.length; i++) {
          final file = imageFiles[i];
          final multipartFile = await http.MultipartFile.fromPath(
            'images',
            file.path,
          );
          request.files.add(multipartFile);
        }
      }

      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);
      final responseBody = json.decode(response.body);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ServiceModel.fromJson(responseBody['service']);
      } else {
        throw Exception(responseBody['message'] ?? 'Failed to update service');
      }
    } catch (e) {
      throw Exception('Failed to update service: ${e.toString()}');
    }
  }

  // Delete service
  static Future<bool> deleteService(String serviceId) async {
    try {
      final headers = await _getHeaders();
      final response = await http.delete(
        Uri.parse('$baseUrl/services/delete/$serviceId'),
        headers: headers,
      );

      final responseBody = json.decode(response.body);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return true;
      } else {
        throw Exception(responseBody['message'] ?? 'Failed to delete service');
      }
    } catch (e) {
      throw Exception('Failed to delete service: ${e.toString()}');
    }
  }
}
